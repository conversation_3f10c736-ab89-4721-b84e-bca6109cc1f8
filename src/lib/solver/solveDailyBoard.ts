/**
 * Main solver implementation for the Letters game
 *
 * This module implements the core solving algorithm using beam search
 * with priority queue and branch-and-bound optimization.
 */

import TinyQueue from 'tinyqueue';
import type { Page } from '@playwright/test';
import { GameState } from '../models/GameState';
import { Board } from '../models/Board';
import { Word } from '../models/Word';
import { GAME_CONFIG, type GameNode, type BestLineResult } from '../types';
import { scrapeBoard, playWord, undoLastMove } from './index';
import { hashGameState, BoardHashSet } from './hashing';
import { upperBoundForGameState, shouldPruneNode } from './optimization';
import { bestWords } from '../bestWords';

/**
 * Solver configuration
 */
interface SolverConfig {
	/** Beam width parameter K */
	beamWidth: number;
	/** Maximum search depth */
	maxDepth: number;
	/** Time limit in milliseconds */
	timeLimit: number;
	/** Enable branch-and-bound pruning */
	usePruning: boolean;
	/** Enable state deduplication */
	useDeduplication: boolean;
	/** Maximum words to consider per position */
	maxWordsPerPosition: number;
	/** Enable debug logging */
	debug: boolean;
}

/**
 * Default solver configuration
 */
const DEFAULT_CONFIG: SolverConfig = {
	beamWidth: 60,
	maxDepth: GAME_CONFIG.MAX_TURNS,
	timeLimit: 180000, // 3 minutes
	usePruning: true,
	useDeduplication: true,
	maxWordsPerPosition: 100,
	debug: false
};

/**
 * Solver statistics
 */
interface SolverStats {
	nodesExplored: number;
	nodesPruned: number;
	statesDeduped: number;
	timeElapsed: number;
	bestScore: number;
	solutionDepth: number;
}

/**
 * Solve the daily board using beam search with branch-and-bound
 */
export async function solveDailyBoard(
	page: Page,
	config: SolverConfig = DEFAULT_CONFIG
): Promise<BestLineResult> {
	const startTime = Date.now();
	const stats: SolverStats = {
		nodesExplored: 0,
		nodesPruned: 0,
		statesDeduped: 0,
		timeElapsed: 0,
		bestScore: 0,
		solutionDepth: 0
	};

	try {
		// Scrape initial board state
		if (config.debug) console.log('[solveDailyBoard] Scraping initial board...');
		const initialBoard = await scrapeBoard(page);
		const initialState = GameState.fromBoard(initialBoard);

		// Initialize search
		const result = await beamSearch(page, initialState, config, stats);

		// Calculate final statistics
		stats.timeElapsed = Date.now() - startTime;
		stats.bestScore = result.total;
		stats.solutionDepth = result.words.length;

		if (config.debug) {
			console.log('[solveDailyBoard] Search completed:', stats);
		}

		return result;
	} catch (error) {
		console.error('[solveDailyBoard] Error during solving:', error);
		throw error;
	}
}

/**
 * Beam search implementation with priority queue
 */
async function beamSearch(
	page: Page,
	initialState: GameState,
	config: SolverConfig,
	stats: SolverStats
): Promise<BestLineResult> {
	// Priority queue for beam search (max-heap by total + upper bound)
	const queue = new TinyQueue<GameNode>([], (a, b) => {
		const scoreA = a.total + a.upperBound;
		const scoreB = b.total + b.upperBound;
		return scoreB - scoreA; // Max-heap
	});

	// State deduplication
	const visitedStates = new BoardHashSet();

	// Best solution found so far
	let bestSolution: BestLineResult = {
		total: 0,
		words: [],
		perRound: []
	};

	// Initialize with root node
	const rootNode: GameNode = {
		board: initialState.board,
		turn: initialState.turn,
		total: initialState.total,
		moves: initialState.moves,
		upperBound: upperBoundForGameState(initialState, config)
	};

	queue.push(rootNode);

	const startTime = Date.now();

	while (queue.length > 0) {
		// Check time limit
		if (Date.now() - startTime > config.timeLimit) {
			if (config.debug) console.log('[beamSearch] Time limit reached');
			break;
		}

		// Get next node
		const currentNode = queue.pop()!;
		stats.nodesExplored++;

		// Check if this is a complete solution
		if (currentNode.turn >= GAME_CONFIG.MAX_TURNS) {
			if (currentNode.total > bestSolution.total) {
				bestSolution = {
					total: currentNode.total,
					words: currentNode.moves.map((move) => move.letters),
					perRound: currentNode.moves.map((move) => ({
						word: move.letters,
						score: move.score
					}))
				};

				if (config.debug) {
					console.log(`[beamSearch] New best solution: ${bestSolution.total}`);
				}
			}
			continue;
		}

		// Pruning check
		if (
			config.usePruning &&
			shouldPruneNode(
				new GameState(currentNode.board, currentNode.turn, currentNode.total, currentNode.moves),
				bestSolution.total,
				config
			)
		) {
			stats.nodesPruned++;
			continue;
		}

		// Deduplication check
		if (config.useDeduplication) {
			if (visitedStates.has(currentNode.board)) {
				stats.statesDeduped++;
				continue;
			}
			visitedStates.add(currentNode.board);
		}

		// Generate successor nodes
		const successors = await generateSuccessors(page, currentNode, config);

		// Add successors to queue (beam search will naturally limit the beam width)
		for (const successor of successors) {
			queue.push(successor);
		}

		// Maintain beam width by keeping only top K nodes
		if (queue.length > config.beamWidth) {
			// Convert to array, sort, and keep top K
			const nodes = [];
			while (queue.length > 0) {
				nodes.push(queue.pop()!);
			}

			// Sort by total + upper bound (descending)
			nodes.sort((a, b) => b.total + b.upperBound - (a.total + a.upperBound));

			// Keep only top K nodes
			for (let i = 0; i < Math.min(config.beamWidth, nodes.length); i++) {
				queue.push(nodes[i]);
			}
		}
	}

	return bestSolution;
}

/**
 * Generate successor nodes for a given game state
 */
async function generateSuccessors(
	page: Page,
	node: GameNode,
	config: SolverConfig
): Promise<GameNode[]> {
	const successors: GameNode[] = [];
	const currentState = new GameState(node.board, node.turn, node.total, node.moves);

	try {
		// Get possible words for current board
		const possibleWords = await bestWords(node.board, Math.min(config.maxWordsPerPosition, 50));

		// Dynamic beam width based on turn
		const K = Math.max(60 / (node.turn + 1), 20) | 0;
		const wordsToTry = possibleWords.slice(0, K);

		for (const word of wordsToTry) {
			try {
				// Simulate playing this word
				const newState = currentState.playMove(word);

				// Calculate upper bound for the new state
				const upperBound = upperBoundForGameState(newState, config);

				// Create successor node
				const successor: GameNode = {
					board: newState.board,
					turn: newState.turn,
					total: newState.total,
					moves: newState.moves,
					upperBound
				};

				successors.push(successor);
			} catch (error) {
				// Skip invalid moves
				if (config.debug) {
					console.warn(`[generateSuccessors] Invalid move: ${word.letters}`, error);
				}
			}
		}
	} catch (error) {
		console.error('[generateSuccessors] Error generating successors:', error);
	}

	return successors;
}

/**
 * Solve with actual browser interaction (for final solution execution)
 */
export async function solveDailyBoardWithExecution(
	page: Page,
	config: SolverConfig = DEFAULT_CONFIG
): Promise<BestLineResult> {
	// First, find the optimal solution
	const solution = await solveDailyBoard(page, config);

	if (config.debug) {
		console.log('[solveDailyBoardWithExecution] Executing solution:', solution);
	}

	try {
		// Execute the solution on the actual game
		for (let i = 0; i < solution.words.length; i++) {
			const wordStr = solution.words[i];

			// We need to reconstruct the word positions
			// This is a simplified approach - in practice, we'd need to track positions
			const currentBoard = await scrapeBoard(page);
			const word = findWordOnBoard(wordStr, currentBoard);

			if (word) {
				await playWord(page, word.positions);

				// Small delay between moves
				await page.waitForTimeout(1000);
			} else {
				console.warn(`[solveDailyBoardWithExecution] Could not find word on board: ${wordStr}`);
			}
		}

		return solution;
	} catch (error) {
		console.error('[solveDailyBoardWithExecution] Error executing solution:', error);
		throw error;
	}
}

/**
 * Find a word on the board (simplified implementation)
 */
function findWordOnBoard(wordStr: string, board: Board): Word | null {
	// This is a simplified implementation
	// In practice, this would use more sophisticated word finding logic

	const tiles = board.getAllTiles();
	const letters = wordStr.split('');

	// Try to find a path that spells the word
	for (const startTile of tiles) {
		if (startTile.letter === letters[0]) {
			const path = findWordPath(letters, board, startTile.row, startTile.col, []);
			if (path) {
				return new Word(wordStr, path);
			}
		}
	}

	return null;
}

/**
 * Recursively find a path that spells the given letters
 */
function findWordPath(
	letters: string[],
	board: Board,
	row: number,
	col: number,
	usedPositions: Array<[number, number]>
): Array<[number, number]> | null {
	if (letters.length === 0) {
		return [];
	}

	const tile = board.getTile(row, col);
	if (!tile || tile.letter !== letters[0]) {
		return null;
	}

	// Check if position already used
	if (usedPositions.some(([r, c]) => r === row && c === col)) {
		return null;
	}

	if (letters.length === 1) {
		return [[row, col]];
	}

	// Try adjacent positions
	const adjacent = board.getAdjacentTiles(row, col);
	for (const adjTile of adjacent) {
		const path = findWordPath(letters.slice(1), board, adjTile.row, adjTile.col, [
			...usedPositions,
			[row, col]
		]);

		if (path) {
			return [[row, col], ...path];
		}
	}

	return null;
}

/**
 * Get solver statistics for the last run
 */
export function getSolverStats(): SolverStats | null {
	// This would be stored globally in a real implementation
	return null;
}

/**
 * Benchmark solver performance
 */
export async function benchmarkSolver(
	page: Page,
	iterations: number = 5,
	config: SolverConfig = DEFAULT_CONFIG
): Promise<{
	averageTime: number;
	averageScore: number;
	averageNodesExplored: number;
	results: BestLineResult[];
}> {
	const results: BestLineResult[] = [];
	const times: number[] = [];
	const scores: number[] = [];
	const nodesExplored: number[] = [];

	for (let i = 0; i < iterations; i++) {
		const startTime = Date.now();
		const result = await solveDailyBoard(page, { ...config, debug: false });
		const endTime = Date.now();

		results.push(result);
		times.push(endTime - startTime);
		scores.push(result.total);
		// nodesExplored would need to be tracked in stats
	}

	return {
		averageTime: times.reduce((sum, time) => sum + time, 0) / times.length,
		averageScore: scores.reduce((sum, score) => sum + score, 0) / scores.length,
		averageNodesExplored: 0, // Placeholder
		results
	};
}
